body.login #shadow_popup {
    opacity: 1;
    display: flex;
}

body.login .logo {
    max-width: 30px;
}

body.login #logoBlock {
    align-items: center;
    margin: 0 auto;
    gap: 10px;
}

body.login .userSelect {
    display: flex;
    flex-direction: column;
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 5px;
    padding: 3px 5px;
    width: 30%;
    cursor: pointer;
    margin: 5px 0;
    transition: background-color 0.15s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

body.login .userSelect .userName,
body.login .userSelect .userHandle {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

body.login .userSelect:hover {
    background-color: var(--black30a);
}

body.login #handleEntryBlock,
body.login #passwordEntry<PERSON>lock,
body.login #passwordRecoveryBlock {
    margin: 2px;
}
